import os
import cv2

# إعداد المجلد الرئيسي للبيانات
DATA_DIR = './data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# قائمة الكلمات العربية المراد جمع بيانات لها
arabic_words = [
    "مرحبا", "شكرا", "من فضلك", "آسف", "نعم",
    "لا", "ماء", "طعام", "بيت", "مدرسة",
    "كتاب", "قلم", "سيارة", "شمس", "قمر"
]

dataset_size = 100  # عدد الصور لكل كلمة

# فتح كاميرا الويب
cap = cv2.VideoCapture(0)

# جمع البيانات للكلمات العربية
for j, word in enumerate(arabic_words):
    # إنشاء مجلد لكل كلمة إذا لم يكن موجودًا
    word_folder = os.path.join(DATA_DIR, f'arabic_word_{j}_{word}')
    if not os.path.exists(word_folder):
        os.makedirs(word_folder)

    print(f'Collecting data for Arabic word: {word} (Class {j})')

    # انتظار المستخدم للضغط على "Q" لبدء جمع الصور
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        cv2.putText(frame, f'Ready to collect: {word}? Press "Q" to start!', (50, 50),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, f'Word {j+1}/15', (50, 100),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
        cv2.imshow('frame', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # جمع الصور للكلمة الحالية
    counter = 0
    while counter < dataset_size:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        # عرض معلومات التقدم على الشاشة
        cv2.putText(frame, f'Collecting: {word}', (50, 50),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, f'Image {counter+1}/{dataset_size}', (50, 100),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
        cv2.putText(frame, f'Word {j+1}/15', (50, 150),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

        cv2.imshow('frame', frame)
        cv2.imwrite(os.path.join(word_folder, f'{counter}.jpg'), frame)
        counter += 1

        if cv2.waitKey(1) & 0xFF == ord('q'):  # السماح بالخروج أثناء جمع الصور
            print("Exiting early...")
            break

    print(f'Completed collecting {counter} images for word: {word}')

cap.release()
cv2.destroyAllWindows()
print("Data collection completed for all 15 Arabic words!")
