import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display

def draw_arabic_text(img, text, position, font_size=30, color=(0, 255, 0)):
    """
    دالة لرسم النص العربي على الصورة
    """
    try:
        # تحويل الصورة من BGR إلى RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        draw = ImageDraw.Draw(pil_img)

        # محاولة استخدام خط عربي، إذا لم يتوفر استخدم الخط الافتراضي
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        # تشكيل النص العربي وترتيبه
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)

        # رسم النص
        draw.text(position, bidi_text, font=font, fill=color)

        # تحويل الصورة مرة أخرى إلى BGR
        img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return img_bgr
    except:
        # في حالة فشل عرض النص العربي، استخدم النص الإنجليزي
        cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        return img

# إعداد المجلد الرئيسي للبيانات
DATA_DIR = './data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# قائمة الكلمات العربية المراد جمع بيانات لها
arabic_words = [
    "هتعمل ", "سكت ", "كله ", "العالم ", "لو",
    "وهخليهم ", "بايديا", "هتكلم ", "ايه؟",
     "يسمعوني!"
]

dataset_size = 100  # عدد الصور لكل كلمة

# فتح كاميرا الويب
cap = cv2.VideoCapture(0)

# جمع البيانات للكلمات العربية
for j, word in enumerate(arabic_words):
    # إنشاء مجلد لكل كلمة إذا لم يكن موجودًا
    word_folder = os.path.join(DATA_DIR, f'arabic_word_{j}_{word}')
    if not os.path.exists(word_folder):
        os.makedirs(word_folder)

    print(f'Collecting data for Arabic word: {word} (Class {j})')

    # انتظار المستخدم للضغط على "Q" لبدء جمع الصور
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        # استخدام الدالة الجديدة لعرض النصوص العربية
        frame = draw_arabic_text(frame, f'استعد لجمع كلمة: {word}', (50, 50), 30, (0, 255, 0))
        frame = draw_arabic_text(frame, 'اضغط Q للبدء', (50, 100), 25, (0, 255, 255))
        frame = draw_arabic_text(frame, f'الكلمة {j+1}/10', (50, 150), 25, (255, 0, 0))
        cv2.imshow('frame', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # جمع الصور للكلمة الحالية
    counter = 0
    while counter < dataset_size:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        # عرض معلومات التقدم على الشاشة باستخدام النصوص العربية
        frame = draw_arabic_text(frame, f'جمع بيانات: {word}', (50, 50), 30, (0, 255, 0))
        frame = draw_arabic_text(frame, f'صورة {counter+1}/{dataset_size}', (50, 100), 25, (255, 0, 0))
        frame = draw_arabic_text(frame, f'الكلمة {j+1}/10', (50, 150), 25, (0, 0, 255))

        cv2.imshow('frame', frame)
        cv2.imwrite(os.path.join(word_folder, f'{counter}.jpg'), frame)
        counter += 1

        if cv2.waitKey(1) & 0xFF == ord('q'):  # السماح بالخروج أثناء جمع الصور
            print("Exiting early...")
            break

    print(f'Completed collecting {counter} images for word: {word}')

cap.release()
cv2.destroyAllWindows()
print("تم الانتهاء من جمع البيانات لجميع الكلمات العربية الـ10!")
