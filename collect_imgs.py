import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display

def draw_arabic_text(img, text, position, font_size=30, color=(0, 255, 0)):
    """
    دالة لرسم النص العربي على الصورة
    """
    try:
        # تحويل الصورة من BGR إلى RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        draw = ImageDraw.Draw(pil_img)

        # محاولة استخدام خط عربي
        font = None
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]

        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                    break
            except:
                continue

        if font is None:
            font = ImageFont.load_default()

        # تشكيل النص العربي وترتيبه
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)

        # تحويل اللون من BGR إلى RGB
        if len(color) == 3:
            rgb_color = (color[2], color[1], color[0])  # BGR to RGB
        else:
            rgb_color = color

        # رسم النص
        draw.text(position, bidi_text, font=font, fill=rgb_color)

        # تحويل الصورة مرة أخرى إلى BGR
        img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return img_bgr
    except Exception as e:
        print(f"Error in draw_arabic_text: {e}")
        # في حالة فشل عرض النص العربي، استخدم النص الإنجليزي
        cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        return img

# إعداد المجلد الرئيسي للبيانات
DATA_DIR = './data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# قائمة الكلمات العربية المراد جمع بيانات لها
arabic_words = [
    "هتعمل ", "سكت ", "كله ", "العالم ", "لو",
    "هخليهم ", "بايديا", "هتكلم ", "ايه؟",
     "!يسمعوني"
]

dataset_size = 100  # عدد الصور لكل كلمة

# فتح كاميرا الويب
cap = cv2.VideoCapture(0)

# التحقق من أن الكاميرا تعمل
if not cap.isOpened():
    print("Error: Could not open camera")
    exit()
else:
    print("Camera opened successfully")

# جمع البيانات للكلمات العربية
for j, word in enumerate(arabic_words):
    # تنظيف اسم الكلمة من الأحرف الخاصة
    clean_word = word.strip().replace('؟', '').replace('!', '').replace(' ', '_')

    # إنشاء مجلد لكل كلمة إذا لم يكن موجودًا
    word_folder = os.path.join(DATA_DIR, f'arabic_word_{j}_{clean_word}')
    if not os.path.exists(word_folder):
        os.makedirs(word_folder)
        print(f"Created folder: {word_folder}")

    print(f'Collecting data for Arabic word: {word} (Class {j})')

    # انتظار المستخدم للضغط على "Q" لبدء جمع الصور
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        # استخدام الدالة المحسنة لعرض النصوص العربية
        frame = draw_arabic_text(frame, f'استعد لجمع كلمة: {word}', (50, 50), 40, (0, 255, 0))
        frame = draw_arabic_text(frame, 'اضغط Q للبدء', (50, 120), 30, (0, 255, 255))
        frame = draw_arabic_text(frame, f'الكلمة {j+1}/10', (50, 180), 30, (255, 0, 0))
        cv2.imshow('frame', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # جمع الصور للكلمة الحالية
    counter = 0
    while counter < dataset_size:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        # عرض معلومات التقدم على الشاشة باستخدام النصوص العربية
        frame = draw_arabic_text(frame, f'جمع بيانات: {word}', (50, 50), 40, (0, 255, 0))
        frame = draw_arabic_text(frame, f'صورة {counter+1}/{dataset_size}', (50, 120), 30, (255, 0, 0))
        frame = draw_arabic_text(frame, f'الكلمة {j+1}/10', (50, 180), 30, (0, 0, 255))

        cv2.imshow('frame', frame)

        # حفظ الصورة مع التحقق من نجاح العملية
        image_path = os.path.join(word_folder, f'{counter}.jpg')
        success = cv2.imwrite(image_path, frame)
        if success:
            print(f"Saved image: {image_path}")
        else:
            print(f"Failed to save image: {image_path}")

        counter += 1

        if cv2.waitKey(1) & 0xFF == ord('q'):  # السماح بالخروج أثناء جمع الصور
            print("Exiting early...")
            break

    print(f'Completed collecting {counter} images for word: {word}')

cap.release()
cv2.destroyAllWindows()
print("تم الانتهاء من جمع البيانات لجميع الكلمات العربية الـ10!")
