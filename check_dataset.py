import pickle
import numpy as np

# قراءة الـ dataset
with open('data.pickle', 'rb') as f:
    data_dict = pickle.load(f)

data = np.array(data_dict['data'])
labels = np.array(data_dict['labels'])
language = data_dict['language']

print(f"Language: {language}")
print(f"Dataset shape: {data.shape}")
print(f"Labels shape: {labels.shape}")
print(f"Number of classes: {len(np.unique(labels))}")
print(f"Classes: {np.unique(labels)}")

# عد عدد العينات لكل فئة
unique_labels, counts = np.unique(labels, return_counts=True)
print("\nSamples per class:")
for label, count in zip(unique_labels, counts):
    print(f"Class {label}: {count} samples")

print(f"\nTotal samples: {len(data)}")
print(f"Feature dimension: {data.shape[1] if len(data) > 0 else 0}")
