import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import os

def draw_arabic_text(img, text, position, font_size=30, color=(0, 255, 0)):
    """
    دالة لرسم النص العربي على الصورة
    """
    try:
        # تحويل الصورة من BGR إلى RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        draw = ImageDraw.Draw(pil_img)
        
        # محاولة استخدام خط عربي
        font = None
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf", 
            "C:/Windows/Fonts/calibri.ttf",
        ]
        
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                    print(f"Using font: {font_path}")
                    break
            except Exception as e:
                print(f"Failed to load font {font_path}: {e}")
                continue
        
        if font is None:
            font = ImageFont.load_default()
            print("Using default font")
        
        # تشكيل النص العربي وترتيبه
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        print(f"Original text: {text}")
        print(f"Reshaped text: {bidi_text}")
        
        # تحويل اللون من BGR إلى RGB
        if len(color) == 3:
            rgb_color = (color[2], color[1], color[0])  # BGR to RGB
        else:
            rgb_color = color
        
        # رسم النص
        draw.text(position, bidi_text, font=font, fill=rgb_color)
        
        # تحويل الصورة مرة أخرى إلى BGR
        img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return img_bgr
    except Exception as e:
        print(f"Error in draw_arabic_text: {e}")
        # في حالة فشل عرض النص العربي، استخدم النص الإنجليزي
        cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        return img

# اختبار عرض النصوص العربية
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("Error: Could not open camera")
    exit()

print("Testing Arabic text display...")
print("Press 'q' to exit")

test_words = ["مرحبا", "شكرا", "من فضلك"]
word_index = 0

while True:
    ret, frame = cap.read()
    if not ret:
        print("Failed to grab frame")
        break
    
    current_word = test_words[word_index % len(test_words)]
    
    # اختبار عرض النصوص العربية
    frame = draw_arabic_text(frame, f'كلمة تجريبية: {current_word}', (50, 50), 40, (0, 255, 0))
    frame = draw_arabic_text(frame, 'اضغط Q للخروج', (50, 120), 30, (0, 255, 255))
    frame = draw_arabic_text(frame, f'الكلمة {word_index + 1}/{len(test_words)}', (50, 180), 30, (255, 0, 0))
    
    cv2.imshow('Arabic Text Test', frame)
    
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord(' '):  # مسطرة للتنقل بين الكلمات
        word_index = (word_index + 1) % len(test_words)

cap.release()
cv2.destroyAllWindows()
print("Test completed!")
