import pickle
import cv2
import mediapipe as mp
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import os

def draw_arabic_text(img, text, position, font, color=(0, 255, 0)):
    """دالة لرسم النص العربي على الصورة"""
    try:
        # تحويل الصورة من BGR إلى RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        draw = ImageDraw.Draw(pil_img)

        # تشكيل النص العربي وترتيبه
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)

        # تحويل اللون من BGR إلى RGB
        rgb_color = (color[2], color[1], color[0]) if len(color) == 3 else color

        # رسم النص
        draw.text(position, bidi_text, font=font, fill=rgb_color)

        # تحويل الصورة مرة أخرى إلى BGR
        img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return img_bgr
    except Exception as e:
        print(f"Error in draw_arabic_text: {e}")
        # في حالة فشل عرض النص العربي، استخدم النص الإنجليزي
        cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        return img

# تحميل النموذج
model_dict = pickle.load(open('./model.p', 'rb'))
model = model_dict['model']

# إعداد MediaPipe
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False, min_detection_confidence=0.5)

# قاموس الكلمات العربية المدربة
labels_dict_ar = {
    0: 'هتعمل',
    1: 'سكت',
    2: 'كله',
    3: 'العالم',
    4: 'لو',
    5: 'هخليهم',
    6: 'بايديا',
    7: 'هتكلم',
    8: 'ايه؟',
    9: 'يسمعوني'
}

print("نظام التعرف على الكلمات العربية بلغة الإشارة")
print("الكلمات المدعومة:")
for key, value in labels_dict_ar.items():
    print(f"  {key}: {value}")
print("\nاضغط 'q' للخروج، مسطرة لإضافة الكلمة للنص المجمع")

# استخدام القاموس العربي فقط
labels_dict = labels_dict_ar
language = 'ar'  # اللغة العربية فقط

# تحميل الخط المناسب
try:
    font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 32)
except:
    try:
        font = ImageFont.truetype("arial.ttf", 32)
    except:
        font = ImageFont.load_default()

# فتح كاميرا الويب
cap = cv2.VideoCapture(0)

# بدء التعرف على الإشارات
collected_text = ""  # النص المجمع
while True:
    ret, frame = cap.read()
    if not ret:
        break

    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = hands.process(frame_rgb)

    if results.multi_hand_landmarks:
        for hand_landmarks in results.multi_hand_landmarks:
            data_aux = []
            x_ = []
            y_ = []

            for i in range(len(hand_landmarks.landmark)):
                x = hand_landmarks.landmark[i].x
                y = hand_landmarks.landmark[i].y
                x_.append(x)
                y_.append(y)

            for i in range(len(hand_landmarks.landmark)):
                x = hand_landmarks.landmark[i].x
                y = hand_landmarks.landmark[i].y
                data_aux.append(x - min(x_))
                data_aux.append(y - min(y_))

            prediction = model.predict([np.asarray(data_aux)])
            predicted_character = labels_dict.get(int(prediction[0]), "غير معروف")

            # عرض الكلمة المتنبأ بها باستخدام النصوص العربية
            frame = draw_arabic_text(frame, f'الكلمة: {predicted_character}', (50, 50), font, (0, 255, 0))

    # عرض النص المجمع
    if collected_text.strip():
        frame = draw_arabic_text(frame, f'النص المجمع: {collected_text}', (10, 400), font, (255, 0, 0))
    else:
        frame = draw_arabic_text(frame, 'اضغط مسطرة لإضافة الكلمة', (10, 400), font, (255, 255, 0))

    cv2.imshow('Arabic Sign Language Recognition', frame)

    key = cv2.waitKey(1) & 0xFF
    if key == ord(' '):  # إذا تم الضغط على المسطرة
        if 'predicted_character' in locals():
            collected_text += predicted_character + " "
            print(f"تمت إضافة: {predicted_character}")
    elif key == ord('q'):  # إذا تم الضغط على Q للخروج
        break
    elif key == ord('c'):  # إذا تم الضغط على C لمسح النص
        collected_text = ""
        print("تم مسح النص المجمع")

cap.release()
cv2.destroyAllWindows()

print(f"Final Collected Text: {collected_text}")
